import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const startDate = req.nextUrl.searchParams.get("startDate");
  const endDate = req.nextUrl.searchParams.get("endDate");
  const gameId = req.nextUrl.searchParams.get("gameId");
  const result = req.nextUrl.searchParams.get("result");

  if (!startDate || !endDate) {
    return NextResponse.json({ message: "缺少日期參數" }, { status: 400 });
  }

  try {
    // For now, we'll simulate draw records based on game records
    // In a real implementation, you'd have a separate DrawRecord table
    const gameRecords = await prisma.gameRecord.findMany({
      where: {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate + "T23:59:59.999Z"),
        },
        gameId: gameId || undefined,
      },
      include: {
        user: {
          select: {
            nickname: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Simulate draw results - in reality this would come from actual draw records
    const drawRecords = gameRecords.map((record) => {
      // Simulate random draw result for demo purposes
      const isWin = Math.random() < 0.1; // 10% win rate for simulation

      return {
        id: record.id,
        userId: record.userId,
        gameId: record.gameId,
        score: Number(record.score),
        result: isWin ? "win" : "lose",
        createdAt: record.createdAt.toISOString(),
        user: record.user,
        coupon: isWin ? { code: `WIN${record.id.slice(-8)}` } : null,
      };
    });

    // Filter by result if specified
    const filteredRecords = result
      ? drawRecords.filter((record) => record.result === result)
      : drawRecords;

    return NextResponse.json(filteredRecords);
  } catch (error) {
    console.error("Error fetching draw records:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
