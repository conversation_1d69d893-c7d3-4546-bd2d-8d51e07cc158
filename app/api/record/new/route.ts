import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userId = session?.user.id;

  if (!userId) {
    return new Response("請先登入", { status: 401 });
  }

  const body = await req.json();
  const { gameId, score } = body;
  await prisma.gameRecord.create({
    data: {
      gameId,
      userId,
      score,
    },
  });

  return new Response("OK");
}
